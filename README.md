# Web Interior Designer

An AI-powered web application that transforms interior spaces using realistic image generation. Upload a photo of any room, select a design style, and receive a professionally redesigned version powered by AI.

## Features

- **Image Upload & Processing**: Upload photos of interior spaces for transformation
- **Multiple Design Styles**: Choose from various interior design styles (Modern, Minimalist, Farmhouse, Scandinavian, etc.)
- **AI-Powered Transformations**: Generate realistic redesigned images using advanced AI models
- **Authentication**: Secure user authentication with Clerk
- **Database Integration**: User data and project management with Supabase
- **Responsive Design**: Modern UI built with React, Chakra UI, and Tailwind CSS

## Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **TanStack Router** for routing
- **Chakra UI** + **Tailwind CSS** for styling
- **Clerk** for authentication
- **Supabase** client for database integration

### Backend
- **FastAPI** (Python) for API endpoints
- **Supabase** for database and authentication
- **JWT** token validation
- **CORS** enabled for development

## Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.8+
- Docker Desktop (for local development)
- Supabase CLI (for local development)
- Supabase account and project (for production)
- Clerk account and application

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd web-interior-designer
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

3. **Backend Setup**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. **Environment Configuration**

   **For Local Development:**
   ```bash
   # Install Supabase CLI (if not already installed)
   brew install supabase/tap/supabase

   # Start local Supabase development environment
   supabase start
   ```

   The local environment files (`.env.local`) are already configured with the correct local Supabase settings.

   **For Production:**
   - Set up your Supabase project and get API keys
   - Configure Clerk authentication
   - Create `.env` files in both frontend and backend directories with production credentials

### Running the Application

#### Local Development (Recommended)

1. **Start Supabase Local Environment**
   ```bash
   # Start local Supabase (run this first)
   supabase start
   ```

2. **Start the Backend**
   ```bash
   cd backend
   # Copy local environment file
   cp .env.local .env
   # Start the backend
   uvicorn main:app --reload --port 8080
   ```

3. **Start the Frontend**
   ```bash
   cd frontend
   # Copy local environment file
   cp .env.local .env
   # Start the frontend
   npm run dev
   ```

4. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080
   - Supabase Studio: http://127.0.0.1:54323
   - Local Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres

#### Production Development

1. **Start the Backend**
   ```bash
   cd backend
   uvicorn main:app --reload --port 8080
   ```

2. **Start the Frontend**
   ```bash
   cd frontend
   npm run dev
   ```

   For development without API calls (dry mode):
   ```bash
   npm run dev:dry
   ```

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080

## Development

### Local Development with Supabase CLI

This project is configured to use Supabase CLI for local development, which provides:

- **Local PostgreSQL Database**: Full-featured PostgreSQL database running locally
- **Local Auth**: Complete authentication system with JWT tokens
- **Local Storage**: File storage system for development
- **Local Edge Functions**: Serverless functions for custom logic
- **Supabase Studio**: Web interface for database management
- **Real-time subscriptions**: WebSocket connections for live updates

#### Supabase CLI Commands

```bash
# Start local development environment
supabase start

# Stop local environment
supabase stop

# Reset local database (clears all data)
supabase db reset

# View local environment status
supabase status

# Generate TypeScript types from your database schema
supabase gen types typescript --local > types/supabase.ts

# Create a new migration
supabase migration new <migration_name>

# Apply migrations to local database
supabase db reset

# View logs from local services
supabase logs
```

#### Local Environment URLs

When running `supabase start`, the following services are available:

- **API URL**: http://127.0.0.1:54321
- **GraphQL URL**: http://127.0.0.1:54321/graphql/v1
- **S3 Storage URL**: http://127.0.0.1:54321/storage/v1/s3
- **Database URL**: postgresql://postgres:postgres@127.0.0.1:54322/postgres
- **Studio URL**: http://127.0.0.1:54323 (Database management interface)
- **Inbucket URL**: http://127.0.0.1:54324 (Email testing interface)

#### Environment Files

- **`.env.local`**: Pre-configured for local development with Supabase CLI
- **`.env`**: Used for production or remote Supabase instances

### Testing
```bash
# Frontend tests
cd frontend
npm run test

# Backend tests
cd backend
python -m pytest
```

### Code Quality
```bash
# Frontend linting and formatting
cd frontend
npm run lint
npm run format
npm run check
```

## Project Structure

```
web-interior-designer/
├── frontend/          # React frontend application
├── backend/           # FastAPI backend application
├── docs/             # Documentation
├── doing/            # Development planning documents
└── prompt-bank/      # AI prompt templates for different styles
```

## Authentication & Security

- User authentication handled by Clerk
- Protected API endpoints require valid JWT tokens
- Supabase Row Level Security for data protection
- CORS configured for development (restrict in production)

## Deployment

The application is designed for cloud deployment with:
- Frontend: Static hosting (Vercel, Netlify, etc.)
- Backend: Container deployment (DigitalOcean, AWS, etc.)
- Database: Supabase (managed PostgreSQL)

See `docs/` directory for detailed deployment guides.
