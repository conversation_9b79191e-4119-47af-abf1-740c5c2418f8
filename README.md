# Web Interior Designer

An AI-powered web application that transforms interior spaces using realistic image generation. Upload a photo of any room, select a design style, and receive a professionally redesigned version powered by AI.

## Features

- **Image Upload & Processing**: Upload photos of interior spaces for transformation
- **Multiple Design Styles**: Choose from various interior design styles (Modern, Minimalist, Farmhouse, Scandinavian, etc.)
- **AI-Powered Transformations**: Generate realistic redesigned images using advanced AI models
- **Authentication**: Secure user authentication with Clerk
- **Database Integration**: User data and project management with Supabase
- **Responsive Design**: Modern UI built with React, Chakra UI, and Tailwind CSS

## Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **TanStack Router** for routing
- **Chakra UI** + **Tailwind CSS** for styling
- **Clerk** for authentication
- **Supabase** client for database integration

### Backend
- **FastAPI** (Python) for API endpoints
- **Supabase** for database and authentication
- **JWT** token validation
- **CORS** enabled for development

## Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.8+
- Supabase account and project
- Clerk account and application

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd web-interior-designer
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

3. **Backend Setup**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   - Set up your Supabase project and get API keys
   - Configure Clerk authentication
   - Create `.env` files in both frontend and backend directories with required credentials

### Running the Application

1. **Start the Backend**
   ```bash
   cd backend
   uvicorn main:app --reload --port 8080
   ```

2. **Start the Frontend**
   ```bash
   cd frontend
   npm run dev
   ```

   For development without API calls (dry mode):
   ```bash
   npm run dev:dry
   ```

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080

## Development

### Testing
```bash
# Frontend tests
cd frontend
npm run test

# Backend tests
cd backend
python -m pytest
```

### Code Quality
```bash
# Frontend linting and formatting
cd frontend
npm run lint
npm run format
npm run check
```

## Project Structure

```
web-interior-designer/
├── frontend/          # React frontend application
├── backend/           # FastAPI backend application
├── docs/             # Documentation
├── doing/            # Development planning documents
└── prompt-bank/      # AI prompt templates for different styles
```

## Authentication & Security

- User authentication handled by Clerk
- Protected API endpoints require valid JWT tokens
- Supabase Row Level Security for data protection
- CORS configured for development (restrict in production)

## Deployment

The application is designed for cloud deployment with:
- Frontend: Static hosting (Vercel, Netlify, etc.)
- Backend: Container deployment (DigitalOcean, AWS, etc.)
- Database: Supabase (managed PostgreSQL)

See `docs/` directory for detailed deployment guides.
